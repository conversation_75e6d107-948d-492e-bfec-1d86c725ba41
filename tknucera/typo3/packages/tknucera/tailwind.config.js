/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ["./Resources/Private/**/*.{html,js}"],
  theme: {
    extend: {
      fontFamily: {
        corporate: ['"CorporateFont"', 'Verdana, Tahoma, Arial, sans-serif'],
        corporateBold: ['"CorporateFont-Bold"', 'Verdana, Tahoma, Arial, sans-serif'],
        corporateItalic: ['"CorporateFont-Italic"', 'Verdana, Tahoma, Arial, sans-serif'],
        corporateBlack: ['"CorporateFont-Black"', 'Verdana, Tahoma, Arial, sans-serif'],
      },
      colors: {
        // only styleguide relevant
        primaryBlueOpacity: 'rgba(0,123,196,0.85)',
        primaryYellowOpacity: 'rgba(247,181,0,0.85)',
        // design relevant
        primaryYellow: {
          DEFAULT: '#F7B500',
          110: '#E7A500',
          80: '#F9C433',
          60: '#FAD366',
          40: '#FCE199',
          20: '#F9ECC9',
        },
        primaryBlue: {
          DEFAULT: '#007BC4',
          140: '#00578b',
          80: '#2E90CB',
          60: '#5CA6D2',
          40: '#89BAD7',
          20: '#B7D0DE',
          10: '#B7D0DE',
        },
        black: '#0E0E10',
        darkGrey: '#353535',
        darkBlue: '#0161B7',
        softWhite: '#F9FAFB',
        grey: {
          DEFAULT: '#575756', // same as 100
          120: '#434343',
          100: '#575756',
          80: '#6D6D6D',
          60: '#989898',
          40: '#B8B9B9',
          20: '#D9D9DA',
          10: '#E9EAEA',
          5: '#F5F5F5',
        },
        red: '#E02424',
        lightRed: '#FDF2F2',
        green: '#0E9F6E',
        lightGreen: '#F3FAF7',
      },
      fontSize: {
        'big': '1.5rem', // 24px
        'big-mobile': '1.25rem', // 20px
        'normal': '1.125rem', // 18px
        'normal-mobile': '1.125rem', // 18px
        'small': '1rem', // 16px
        'small-mobile': '1rem', // 16px
        'extrasmall': '0.875rem', // 14px
        'extrasmall-mobile': '0.875rem', // 14px
        'h1': '2.875rem', // 46px
        'h1-mobile': '2.25rem', // 36px
        'h2': '2.375rem', // 38px
        'h2-mobile': '1.875rem', // 30px
        'h3': '1.625rem', // 26px
        'h3-mobile': '1.5rem', // 24px
        'subtitle': '1.25rem', // 20px
        'subtitle-mobile': '1.25rem', // 20px
      },
      lineHeight: {
        'big': '2.25rem', // 36px
        'big-mobile': '1.875rem', // 30px
        'normal': '1.6875rem', // 27px
        'normal-mobile': '1.6875rem', // 27px
        'small': '1.5rem', // 24px
        'small-mobile': '1.5rem', // 24px
        'extrasmall': '1.125rem', // 17.5px
        'extrasmall-mobile': '1.125rem', // 17.5px
        'h1': '3.75rem', // 60px
        'h1-mobile': '2.925rem', // 46.8px
        'h2': '3.125rem', // 50px
        'h2-mobile': '2.4375rem', // 39px
        'h3': '2.3125rem', // 37px
        'h3-mobile': '2rem', // 32px
        'subtitle': '1.75rem', // 28px
        'subtitle-mobile': '1.875rem', // 30px
      },
      aspectRatio: {
        '1x1': '1 / 1',
        '4x3': '4 / 3',
        '16x9': '16 / 9',
        '16x10': '16 / 10',
        '3x2': '3 / 2',
        '3x1': '3 / 1',
        '2x1': '2 / 1',
        '5x2': '5 / 2',
        'golden': '1 / 1.618',
        '2x3': '2 / 3',
        '3x4': '3 / 4',
      },
      padding: {
        '4-2px': '14px',
        '3.5': '0.875rem'
      },
      transitionProperty: {
        'height': 'max-height',
        'spacing': 'margin, padding',
      },
      containers: {
        'mobile': '45.9375rem', // 767px - 2rem (32px)
      },
    }
  },
  plugins: [],
}

