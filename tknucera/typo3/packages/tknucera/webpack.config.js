const Encore = require('@symfony/webpack-encore');
const SVGSpritemapPlugin = require('svg-spritemap-webpack-plugin');

// Manually configure the runtime environment if not already configured yet by the "encore" command.
// It's useful when you use tools that rely on webpack.config.js file.
if (!Encore.isRuntimeEnvironmentConfigured()) {
    Encore.configureRuntimeEnvironment(process.env.NODE_ENV || 'dev');
}

function getFileNameConfigForEnv(isProduction = true) {
    return {
        'js': isProduction ? '[name].[contenthash:8].min.js' : '[name].js',
        'css': isProduction ? '[name].[contenthash:8].min.css' : '[name].css'
    }
}

Encore
    // directory where compiled assets will be stored
    .setOutputPath('./Resources/Public/Assets')
    // public path used by the web server to access the output path

    // NOTE: The hash (b42abcc7cf2f7d76bdce5e98ff4f5f95)
    // SEE: https://t3planet.de/blog/migrieren-sie-typo3conf-zu-assets-in-composer-typo3-v12/
    // is based on the vendor structure and so the path is valid until the path
    // vendor/mogic/tknucera/Resources/Public/Assets/
    // changed or is not available anymore
    .setPublicPath('/_assets/b42abcc7cf2f7d76bdce5e98ff4f5f95/Assets/')

    // only needed for CDN's or sub-directory deploy
    .setManifestKeyPrefix('vendor/mogic/tknucera')

    /*
     * ENTRY CONFIG
     *
     * Add 1 entry for each "page" of your app
     * (including one that's included on every page - e.g. "app")
     *
     * Each entry will result in one JavaScript file (e.g. app.js)
     * and one CSS file (e.g. app.css) if your JavaScript imports CSS.
     */
    .addEntry('tknucera', './Resources/Private/Assets/tknucera.js')

    // When enabled, Webpack "splits" your files into smaller pieces for greater optimization.
    .splitEntryChunks()

    // will require an extra script tag for runtime.js
    // but, you probably want this, unless you're building a single-page app
    .disableSingleRuntimeChunk()

    /*
     * FEATURE CONFIG
     *
     * Enable & configure other features below. For a full
     * list of features, see:
     * https://symfony.com/doc/current/frontend.html#adding-more-features
     */
    .cleanupOutputBeforeBuild()
    .enableBuildNotifications()
    .enableSourceMaps(!Encore.isProduction())
    // enables hashed filenames (e.g. app.abc123.css)
    .enableVersioning(Encore.isProduction())
    .configureFilenames(
        getFileNameConfigForEnv(Encore.isProduction())
    )
    .configureFontRule({
        filename: 'Fonts/[name][ext]'
    })

    // enables @babel/preset-env polyfills
    .configureBabelPresetEnv((config) => {
        config.useBuiltIns = 'usage';
        config.corejs = 3;
    })

    // enables Sass/SCSS support
    .enableSassLoader()

    // enables PostCss support
    .enablePostCssLoader((options) => {
        options.postcssOptions = {
            // directory where the postcss.config.js file is stored
            config: './postcss.config.js',
        };
    })

    //SVG Sprite Map Plugin (all icons, old way, deprecated)
    .addPlugin(new SVGSpritemapPlugin('./Resources/Private/Assets/Icons/**/*.svg',
        {
            output : {
                filename: 'spritemap.svg',
                svgo: false,
                svg4everybody: {
                    polyfill: true
                }
            },
            sprite: {
                generate: {
                    title: false
                }
            }
        }
    ))

    // uncomment if you use TypeScript
    //.enableTypeScriptLoader()

    // uncomment to get integrity="..." attributes on your script & link tags
    // requires WebpackEncoreBundle 1.4 or higher
    //.enableIntegrityHashes(Encore.isProduction())

    // uncomment if you use API Platform Admin (composer require api-admin)
    //.enableReactPreset()
    //.addEntry('admin', './assets/admin.js')
;

let config = Encore.getWebpackConfig();

module.exports = config;
